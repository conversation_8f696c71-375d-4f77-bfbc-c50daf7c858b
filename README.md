# @baiducloud/runtime

新架构下的模块公共模板与路由等

## 本地调试
本地业务模块开发需同步开发调试Runtime时，按照已下步骤（任何模板更改建议在demo库中加一个示例）：
1. 运行启动runtime库：在Runtime下运行
```
    npm run dev
```
2. 在业务模块中使用本地runtime模块：在业务模块下运行
```
    npx bce-cli dev -R http://localhost:8888/runtime.js
```

## 一些原则

> 关于装饰器

- 装饰器应该是最高优被定义
- 装饰器本身应该是一个元数据声明的纯函数，不应该依赖服务、模板等其他执行逻辑

> 关于服务

- 服务应该仅次于装饰器被声明，构造函数不应该依赖其他服务

> 关于作用域

- 组件以及服务默认为全局
- 组件以及服务指定作用域将在指定的作用域下优先生效，如果没有则降级为全局，再找不到就报错了
