{"name": "@baiducloud/runtime", "version": "1.0.0-rc.41", "description": "", "main": "dist/runtime.js", "types": "dist/types/index.d.ts", "scripts": {"ts-check": "npx tsc -w", "ts:defs-gen": "tsc --declaration --outDir dist/types-split --emitDeclarationOnly", "ts:defs-bdl": "npx dts-bundle --name @baiducloud/runtime --main dist/types-split/index.d.ts --out ../types/index.d.ts", "prepublishOnly": "sh scripts/pre-publish.sh", "dev": "NODE_OPTIONS=--openssl-legacy-provider npx bce-cli dev --sdk", "build": "npx bce-cli build --sdk"}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/baiducloud/fe-runtime"}, "author": "zhanghao25", "license": "MIT", "dependencies": {"html5parser": "^2.0.2", "typescript": "^4.1.3"}, "devDependencies": {"@baidu/bce-cli": "0.0.0-beta.31", "@baidu/reflect-metadata": "^1.0.1", "@baiducloud/i18n": "^1.0.0-rc.24", "@types/lodash": "^4.14.149", "core-decorators": "^0.20.0", "core-js": "^3.6.4", "dts-bundle": "^0.7.3", "eslint": "^5.16.0", "eslint-config-airbnb-base": "^13.2.0", "eslint-formatter-pretty": "^2.1.0", "eslint-plugin-import": "^2.20.1", "events": "^3.1.0", "regenerator-runtime": "^0.13.3", "san": "^3.8.1", "san-router": "^1.2.2"}}