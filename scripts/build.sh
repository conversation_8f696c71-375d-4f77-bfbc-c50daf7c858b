# export PATH=$NODEJS_BIN_LATEST:$PATH
export NODE_OPTIONS=--openssl-legacy-provider
echo "node: $(node -v)"
echo "npm: v$(npm -v)"

npm install

NODE_ENV=production npm run build

# babellrc中配置的i18n目录
I18N_OUTPUT_FOLDER=./dist/i18n
if [ -d "$I18N_OUTPUT_FOLDER" ]; then
    if [[ $AGILE_MODULE_NAME ]] && [[ "$AGILE_COMPILE_BRANCH" = "master" ]];then
        npx bce-i18n upload -d "$I18N_OUTPUT_FOLDER" --module COMMON
    else
        echo "请在master分支的Agile流水线中执行该脚本，或设置环境变量AGILE_MODULE_NAME、AGILE_COMPILE_BRANCH、AGILE_REVISION、AGILE_TRIGGER_USER"
    fi
    rm -rf "$I18N_OUTPUT_FOLDER"
else
    echo "未发现国际化内容，可能是babel存在cache，请在生产环境下禁用cache"
fi

mkdir output && tar -cvf output/output.tar dist
