/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * DOM主骨架管理
 *
 * @file container.ts
 * <AUTHOR> (<EMAIL>)
 */

import { Component, ComponentConstructor } from 'san';

import './app-view.less';
import PerfLog from '../perflog';
import { SidebarFactory } from '../factory';
import Processor from '../runtime/processor';

let globalSidebar: any = null;

export default (Klass: any, rule: string): ComponentConstructor<{}, {}> => {
    class AppView extends Component {
        static template = `
            <div class="app-view ${Klass.pageName ? `${Klass.pageName}-view` : ''}">
                <biz-app class="biz-app" route="{{route}}" s-ref="biz-app"/>
            </div>
        `;

        static components = {
            'biz-app': Processor.autowire(Klass)
        };

        @PerfLog.markLifeCircle('san-compiled', { detail: rule })
        compiled() {}

        @PerfLog.markLifeCircle('san-inited')
        inited() {
            const [Sidebar, metadata] = SidebarFactory.fromCmpt(Klass);

            // 如果没有侧边栏则清理掉
            if (!Sidebar) {
                if (globalSidebar) {
                    globalSidebar.dispose();
                    globalSidebar = null;
                }

                return;
            }

            // 如果侧边栏变化了
            if (globalSidebar && !(globalSidebar instanceof Sidebar)) {
                globalSidebar.dispose();
                globalSidebar = null;
            }

            if (!globalSidebar) {
                const Klass = Processor.autowire(Sidebar);

                globalSidebar = new Klass({
                    data: metadata
                });
                globalSidebar.attach(document.getElementById('main'));
            } else {
                Object.entries(metadata).forEach(item => globalSidebar.data.set(...item));
            }
        }

        @PerfLog.markLifeCircle('san-created')
        created() {}

        @PerfLog.markLifeCircle('san-routed')
        route() {}

        @PerfLog.markLifeCircle('san-attached')
        attached() {}

        @PerfLog.markLifeCircle('san-detached')
        detached() {}

        @PerfLog.markLifeCircle('san-disposed')
        disposed() {}

        @PerfLog.markLifeCircle('san-updated')
        updated() {}

        refBizApp() {
            return this.ref('biz-app');
        }
    }

    return AppView;
};
