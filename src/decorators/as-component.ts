/**
 * 标注局部组件
 *
 * @file as-components.js
 * <AUTHOR>
 */

import { defineComponent, Component } from 'san';

import { CmptFactory } from '../factory';
import { isString } from '../helpers/utils';
import { <PERSON>mpt<PERSON>ey, CmptCategory, PrepareKey, PrepareDataKey } from '../helpers/types';

export const invokeCmpt = (...args: string[]): ClassDecorator => {
    return <ClassDecorator>((Klass: any) => {
        args.forEach(cmpt => {
            const mapdata = isString(cmpt) ? { [cmpt.replace(/^@/, '')]: cmpt } : {};

            Reflect.defineMetadata(CmptKey, Object.assign(mapdata, Reflect.getMetadata(CmptKey, Klass)), Klass);
        });

        return Klass;
    });
};

// compatible alias
export const invokeComp = invokeCmpt;

export const asComponent = (meta: string | DecoratorMetaType): ClassDecorator => {
    return <ClassDecorator>((Klass: any) => {
        Reflect.defineMetadata(CmptCategory, isString(meta) ? { name: meta } : meta, Klass);

        CmptFactory.register(Klass);

        return Klass;
    });
};

const loading = defineComponent({ template: `<div class="skeleton">加载中...</div>` });
export function prepare<T = any>(asyncLoader = () => {}, placeholder = loading, fallback = null): ClassDecorator {
    return <ClassDecorator>((target: Object, propertyKey: string, desc: TypedPropertyDescriptor<(data?: T) => any>) => {
        if (target instanceof Component && propertyKey === 'initData') {
            Reflect.defineMetadata(PrepareKey, [asyncLoader, placeholder, fallback], target.constructor);

            return {
                get() {
                    return () => desc.value?.call(this, Reflect.getMetadata(PrepareDataKey, target.constructor));
                }
            };
        }

        throw new TypeError('decorator prepare 只能用于`initData`函数！');
    });
}
