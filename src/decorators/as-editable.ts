/**
 * 标准一个组件是可编辑的
 *
 * @file as-editable.js
 * <AUTHOR>
 */

import { ComponentConstructor } from 'san';
import Editor from '../editable/app-editor';

export const editable = (Klass: ComponentConstructor<any, any>): ComponentConstructor<any, any> =>
    class extends Klass {
        attached() {
            super.attached();

            this.editor = new Editor({
                owner: this
            });
            this.editor.attach(document.body);
        }
    };

export const asEditable = editable;
