/**
 * 标注一个组件为一个页面组件
 *
 * @file page.js
 * <AUTHOR>
 */

import { router } from 'san-router';

import { AppView } from '../app-view';
import PageFactory from '../factory/page-factory';
import { PageCatetory, RouterKey } from '../helpers/types';
import { isString } from '../helpers/utils';
import {collectRoutes} from '../helpers/router';

export default (...rules: Array<string | DecoratorMetaType>): ClassDecorator => (Klass: any) => {
    Reflect.defineMetadata(PageCatetory, true, Klass);
    if (window.amis) {
        collectRoutes(rules.map(rule => isString(rule) ? rule : rule.name));
    }
    Reflect.defineMetadata(
        RouterKey,
        rules.map(x => (isString(x) ? { name: x } : x)),
        Klass
    );

    // 如果发现根节点是一个组件的话则套一个template
    Klass.template = /^\s*<[\w]+-/i.test(Klass.template) ? `<template>${Klass.template}</template>` : Klass.template;

    // TODO(放在这里不合适)
    Reflect.getMetadata(RouterKey, Klass).forEach((rule: DecoratorMetaType) =>
        router.add({
            rule: rule.name,
            Component: () => Promise.resolve(AppView(Klass, rule.name)),
            target: '#main'
        })
    );

    PageFactory.register(Klass);

    return Klass;
};
