/**
 * Azeroth - Decorators - Invoke
 *
 * @file Invoke.js
 * <AUTHOR>
 */

import { ServiceFactory } from '../factory';
import { isString, once } from '../helpers/utils';
import { ServiceKey, ServiceCategory } from '../helpers/types';

const deprecateWarn = once((name: string) =>
    console.error(
        `%c💥\`invokeService\` 已废弃，使用 \`@decorators.service('${name}') ${name}: ServiceType;\``,
        'color: red'
    )
);

export const invokeService = (...services: any[]): ClassDecorator => (Klass: any) => {
    Reflect.defineMetadata(ServiceKey, services, Klass);

    deprecateWarn(services.join(','));

    return Klass;
};

export const asService = (meta: string | DecoratorMetaType): ClassDecorator => {
    return <ClassDecorator>((Klass: any) => {
        Reflect.defineMetadata(ServiceCategory, isString(meta) ? { name: meta } : meta, Klass);

        ServiceFactory.registerServiceKlass(Klass);

        return Klass;
    });
};

export function service(name: string): PropertyDecorator {
    return (target: any, propertyKey: string | symbol) => {
        return {
            get() {
                return ServiceFactory.resolve(name);
            }
        };
    };
}
