/**
 * 标注一个组件为一个页面组件
 *
 * @file page.js
 * <AUTHOR>
 */

import { SidebarFactory } from '../factory';
import { isPagemakerReleaseState, isString } from '../helpers/utils';
import { SidebarKey, SidebarCategory, CmptCategory, CmptKey, DefaultSidebarName } from '../helpers/types';
import { IRoute, registerRoutes } from '../helpers/router';
import { lowerCamelCase, getVariable } from '../helpers/utils';
import { bindEvent } from '../helpers/customEvent';
import { parse, walk, SyntaxKind } from 'html5parser';

interface INode {
    name?: string;
    type: string;
    [propName: string]: any;
}

const navList: Array<IRoute> = [];
export const withSidebar = (...args: any[]): ClassDecorator => {
    return <ClassDecorator>((Klass: any) => {
        if (!isString(args[0])) {
            args.unshift(DefaultSidebarName);
        }

        Reflect.defineMetadata(SidebarKey, args, Klass);

        return Klass;
    });
};

/**
 * @description:
 * @param {**} html 通过parse包裹的html内容
 * @return {**} ast语法树转义后的数据结构
 */
export function parseTemplateToAst(html: string): Array<IRoute> {
    // 进行ast语法树解析
    walk(parse(html), {
        enter: (node: INode) => {
            const navIdentifier = ['app-sidebar', 's-app-sidebar', 's-sidebar'];
            if (Array.isArray(node.body) && navIdentifier.indexOf(node.name) > -1) {
                getHtmlData(node.body, navList);
            }
        }
    });
    registerRoutes(navList);
    return navList;
}

/**
 * @description: 获取html中data数据
 * @param {**} nodes ast语法解析后的节点数据
 * @param {**} targetDatas 接收处理后的变量
 * @return {**} null
 */
function getHtmlData(nodes: Array<any>, targetDatas: Array<IRoute>) {
    nodes.forEach((item: INode) => {
        const navItemIdentifier = ['app-sidebar-item', 's-app-sidebar-item', 's-sidebar-item'];
        if (item.type?.toLowerCase() === 'tag' && navItemIdentifier.indexOf(item.name) > -1) {
            const NavItem: any = { isMenu: true, title: '' };
            const { type, attributes } = item;
            attributes.forEach((item: any) => {
                let { name, value } = item;
                const navName: string = lowerCamelCase(name.value);
                // 接入PageMaker平台暂不支持菜单通过变量获取，如有需求，可通过PageMaker实现
                const valReg = /\{\{.*?\}\}/;
                if (!valReg.test(value.value)) {
                    NavItem[navName] = getVariable(value.value);
                }
            });
            if (item.body) {
                NavItem['children'] = NavItem.children || [];
                getHtmlData(item.body, NavItem.children);
            }
            if (NavItem.title) {
                targetDatas.push(NavItem);
            }
        }
    });
}

export const asSidebar = (name?: string): ClassDecorator => {
    return <ClassDecorator>((Klass: any) => {
        if (window.amis) {
            parseTemplateToAst(Klass.template);
        } else if (isPagemakerReleaseState()) {
            return;
        } else {
            Reflect.defineMetadata(CmptCategory, name || DefaultSidebarName, Klass);
            Reflect.defineMetadata(SidebarCategory, name || DefaultSidebarName, Klass);
            Reflect.defineMetadata(
                CmptKey,
                {
                    'app-sidebar': '@app-sidebar',
                    'app-sidebar-item': '@app-sidebar-item'
                },
                Klass
            );
            // 如果发现根节点是一个组件的话则套一个template
            Klass.template = /^\s*<[\w]+-/i.test(Klass.template)
                ? `<template>${Klass.template}</template>`
                : Klass.template;
            SidebarFactory.register(Klass);
        }
    });
};
