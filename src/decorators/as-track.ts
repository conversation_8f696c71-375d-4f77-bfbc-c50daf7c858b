
/**
 * track装饰器
 *
 * @file as-track.ts
 * <AUTHOR>
 */

import { isFunciton, isPromise, isObject } from '../helpers/utils';

interface TrackData {
    trackId: string;
    trackName: string;
    action?: string;
    value?: any;
    content?: string;
}

interface TrackFunc {
    (args: any[], res: any): TrackData | void;
}

export const TrackLog = (func: TrackFunc | TrackData): MethodDecorator => {
    return <MethodDecorator>((target, propertyKey, descriptor: TypedPropertyDescriptor<Function>) => {
        // 当全局挂载$bceTrack的时候进行上报
        if ((window as any).$bceTrack && isFunciton((window as any).$bceTrack.trackLog)) {
            let originFn = descriptor.value;
            descriptor.value = function (...args: any[]) {
                let result = originFn!.apply(this, args);

                if (isObject(func)) {
                    (window as any).$bceTrack.trackLog(func);
                }

                if (isFunciton(func)) {
                    if (isPromise(result)) {
                        result.then((res: any) => {
                            let data = func.call(this, args, res);
                            data && (window as any).$bceTrack.trackLog(data);
                        });
                    } else {
                        let data = func.call(this, args, result);
                        data && (window as any).$bceTrack.trackLog(data);
                    }
                }

                return result;
            };
        }

        return descriptor;
    });
};
