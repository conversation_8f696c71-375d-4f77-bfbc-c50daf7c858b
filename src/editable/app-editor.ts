/**
 * app-editor
 *
 * @file app-editor.js
 * <AUTHOR>
 */

import { Component } from 'san';

export default class extends Component {
    static template = `
        <div></div>
    `;

    attached() {
        this.owner.el.addEventListener('mouseover', evt => {
            if (evt.metaKey) {
                console.log(this.owner.el);
                this.owner.el.style = 'border: 1px solid #108cee;';
            }
        });

        this.owner.el.addEventListener('mouseout', () => {
            this.owner.el.style = '';
        });
    }
}
