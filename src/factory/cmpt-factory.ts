/**
 * Component factory
 *
 * @file Component-factory.js
 * <AUTHOR>
 */

import { ComponentConstructor } from 'san/types';
import { createComponentLoader } from 'san';

import processor from '../runtime/processor';
import ServiceFactory from './service-factory';
import { Cmpt<PERSON>ey, CmptCategory, PrepareKey, PrepareDataKey } from '../helpers/types';

const __Cache: { [x: string]: Array<ComponentConstructor<any, any>> } = {};

export default class CmptFactory {
    static createAsyncLoader(Klass: any) {
        if (Reflect.hasMetadata(PrepareKey, Klass)) {
            const [createAsyncLoader, placeholder, fallback] = Reflect.getMetadata(PrepareKey, Klass);
            const result = createAsyncLoader.call(Klass, ServiceFactory);

            if (typeof result.then === 'function') {
                return createComponentLoader({
                    placeholder,
                    load() {
                        return result.then(
                            (res: any) => {
                                Reflect.defineMetadata(PrepareData<PERSON><PERSON>, res, Klass);
                                return Klass;
                            },
                            () => Promise.reject(fallback)
                        );
                    }
                });
            }
        }

        return Klass;
    }

    static register(Klass: ComponentConstructor<any, any>) {
        const { name } = Reflect.getMetadata(CmptCategory, Klass);

        if (Array.isArray(__Cache[name])) {
            __Cache[name].push(CmptFactory.createAsyncLoader(Klass));
        } else {
            __Cache[name] = [CmptFactory.createAsyncLoader(Klass)];
        }
    }

    static resolve(name: any): ComponentConstructor<any, any> | undefined {
        if (!Array.isArray(__Cache[name]) || __Cache[name].length === 0) {
            console.error(`[runtime warn]: Unknown component: <${name}>\n-- This component may not register or import properly.`);
            return;
        }

        return processor.autowire(__Cache[name][0]);
    }

    static resloveCmpt(Klass: ComponentConstructor<any, any>) {
        const cmpts = Reflect.getMetadata(CmptKey, Klass);

        if (cmpts) {
            const cmptMap: {[key: string]: ComponentConstructor<any, any> | undefined} = {};

            for (const key in cmpts) {
                // eslint-disable-line
                cmptMap[key] = CmptFactory.resolve(cmpts[key]);
            }

            return cmptMap;
        }

        return {};
    }

    static hasCmptKey(Klass: ComponentConstructor<any, any>) {
        return !!Reflect.getMetadata(CmptKey, Klass);
    }

    static getEntries() {
        return __Cache;
    }
}
