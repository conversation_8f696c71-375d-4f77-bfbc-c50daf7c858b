/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file page-factory.ts
 * <AUTHOR> (<EMAIL>)
 */

import { ComponentConstructor } from "san/types";
import { RouterKey } from "../helpers/types";

const __Cache: Map<
    DecoratorMetaType,
    ComponentConstructor<any, any>
> = new Map();

export default class PageFactory {
    static register(Klass: ComponentConstructor<any, any>) {
        const metadatas: Array<DecoratorMetaType> = Reflect.getMetadata(
            Router<PERSON>ey,
            Klass
        );

        metadatas.forEach((meta) => __Cache.set(meta, Klass));
    }

    static getEntries() {
        return __Cache;
    }
}
