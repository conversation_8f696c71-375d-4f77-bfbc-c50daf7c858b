/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file serv-factory.ts
 * <AUTHOR> (<EMAIL>)
 */

import { ServiceCategory } from '../helpers/types';

let __mutex: null | string = null;
// TODO(zhanghao25): 挂在Object.prototype下太恶心了
const __Global = Object.prototype;
const __ServiceInstanceCache: Map<string, { new (...args: any[]): any }> = new Map();
const __ServiceKlassCache: Map<string, { new (...args: any[]): any }> = new Map();

export default class ServiceFactory {
    /**
     * 负责创建一个新的服务实例
     *
     * @static
     * @param {string} name
     * @param {...any[]} args
     * @returns {*}
     * @memberof ServiceFactory
     */
    static create(name: string, ...args: any[]): any {
        if (__ServiceKlassCache.has(name)) {
            const ServiceKlass = __ServiceKlassCache.get(name)!;
            return new ServiceKlass(...args);
        }

        throw new Error(`不能创建服务${name}!`);
    }

    /**
     * 注册一个新的服务，如果存在老服务则替换掉
     *
     * @static
     * @param {string} name
     * @param {*} Klass
     * @memberof ServiceFactory
     */
    static register(name: string, Klass: any) {
        __ServiceInstanceCache.set(name, Klass);
    }

    /**
     * 取回一个注册服务实例
     *
     * @static
     * @param {string} name
     * @returns
     * @memberof ServiceFactory
     */
    static resolve(name: string): any {
        return __ServiceInstanceCache.get(name);
    }

    /**
     * 注册服务Klass
     *
     * @static
     * @param {*} Klass
     * @returns
     * @memberof ServiceFactory
     */
    static registerServiceKlass(Klass: any) {
        const { name } = Reflect.getMetadata(ServiceCategory, Klass);

        __ServiceKlassCache.set(name, Klass);
        ServiceFactory.register(
            name,
            (() => {
                if (__mutex) {
                    console.warn(`%c💥 服务(${__mutex})构造过程中不应该依赖服务(${name})`, 'color: red');
                }

                __mutex = name;

                const value = ServiceFactory.create(name);

                __mutex = null;

                return value;
            })()
        );

        if (!__Global.hasOwnProperty(name)) {
            Object.defineProperty(__Global, name, {
                get() {
                    return ServiceFactory.resolve(name);
                }
            });
        }
    }
}
