/**
 * sidebar factory
 *
 * @file sidebar-factory.js
 * <AUTHOR>
 */

import { SanComponent } from 'san/types';
import { SidebarCategory, SidebarKey } from '../helpers/types';

const __sidebarCache: any = {};

export default class SidebarFactory {
    static register(Klass: SanComponent<{}>) {
        const name = Reflect.getMetadata(SidebarCategory, Klass);

        if (name) {
            __sidebarCache[name] = Klass;
        }
    }

    static reslove(name: string) {
        return __sidebarCache[name];
    }

    static fromCmpt(Klass: SanComponent<{}>) {
        const metadata = Reflect.getMetadata(SidebarKey, Klass);

        if (metadata) {
            const [name, data] = metadata;
            const SidebarCmpt = SidebarFactory.reslove(name);

            return [SidebarCmpt, data];
        }

        return [];
    }
}
