/**
 * @Author: 
 * @Date: 2022-03-15 17:43:02
 * @Description: 
 */

/**
 * @description: 创建自定义事件方法
 * @param {string} type 事件类型
 * @return {**} null
 */
export function bindEvent(type: string) {
    /**
     * @description: 派发对应事件
     * @param {any} data 需要派发的数据
     * @return {**} null
     */    
    function dispatch(data: any) {
        const event = new Event(type);
        event.data = data;
        window.disPatchEvent(event);
    }

    /**
     * @description: 装饰computed计算属性
     * @param {**} target 类的原型对象
     * @param {**} methodName 类方法的名字
     * @param {**} descriptor 描述信息
     * @return {**}
     */    
    function computedAdaptor(target: any, methodName: string, descriptor: PropertyDescriptor) {
        let oldMethod = descriptor.value;
        descriptor.value = function (...args) {
            const data = oldMethod.apply(this, args)
            return data;
        }
    }

    /**
     * @description: 可以作为装饰器或者方法调用，装饰器主要为了处理computed的计算属性的数据变化
     * @param {array} args 数据或者实例属性方法装饰器参数
     * @return {**}
     */    
    return (...args) => {
        if (args.length === 0) {
            console.error('The number of parameters must be greater than 0');
        } else if (args.length === 1) {
            dispatch(args[0]);
        } else {
            return computedAdaptor(...args);
        }
    }
}
