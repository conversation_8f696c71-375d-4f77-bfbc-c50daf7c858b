/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file decorators.ts
 * <AUTHOR> (<EMAIL>)
 */

export function deprecate(message: string): MethodDecorator {
    return (target, key, desc) => {
        console.warn(`@deprecate: ${message}`);
        return desc;
    }
}

export function fatal(message: string): MethodDecorator {
    return (target, key, desc) => {
        console.error(`@fatal: ${message}`);
        return desc;
    }
}
