/**
 * Copyright (c) 2019 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file interfaces.ts
 * <AUTHOR> (<EMAIL>)
 */

import { Component } from 'san';
import { router } from 'san-router';
import { redirect } from '../helpers/utils';
import { ListPageCatetory, DetailPageCatetory, CreatePageCategory } from '../helpers/types';

abstract class BasePage extends Component {
    /**
     * Page classnames
     */
    static pageName: string;

    /**
     * 浏览器标签页标题 // TODO
     */
    abstract pageTitle?: string;

    abstract attached(): void;

    /**
     * 业务方页面可以针对region做一些处理
     * 具体有三种：
     *  1. 页面数据刷新
     *      适用场景：适用于列表页、创建页等页面，切换后需要刷新列表资源等
     *      业务处理：将所有需要刷新数据的操作放到attached方法中
     *      框架处理：调用页面组件attach方法进行页面的刷新
     *  2. 页面跳转：
     *      适用场景：详情页面，由于region切换后没有此资源，需要从资源的详情页跳回到列表页
     *      业务处理：在页面组件中设置`REGION_CHANGE_LOCATION`属性，该属性的值填写规则：
     *          同模块需要写hash：#/bcc/list
     *          不同模块需要写path + hash，如bcc中跳转cdn: /cdn/#/cdn/domain/list
     *      框架处理：根据REGION_CHANGE_LOCATION做页面跳转
     *  3. 自定义处理：
     *      适用场景：业务需要刷新部分数据或做一些特殊处理
     *      业务处理：在页面组件中设置`onRegionChange`方法，在方法中做处理
     *      框架处理：调用业务方onRegionChange
     */
    REGION_CHANGE_LOCATION?: string;

    onRegionChange() {
        if (this.REGION_CHANGE_LOCATION) {
            redirect(this.REGION_CHANGE_LOCATION);
        } else {
            this.attached();
        }
    }

    /**
     * 业务方页面可以针对组织项目做一些处理
     * 具体有三种：
     *  1. 页面数据刷新
     *      适用场景：适用于列表页、创建页等页面，切换后需要刷新列表资源等
     *      业务处理：将所有需要刷新数据的操作放到inited或attached或refersh或reload方法中
     *      框架处理：调用页面组件该方法进行页面的刷新
     *  2. 页面跳转：
     *      适用场景：详情页面，切换组织项目，需要从详情页跳回到列表页
     *      业务处理：在页面组件中设置`WITH_ORG`或者`WITH_PRO`属性，该属性的值填写规则：
     *          同模块需要写hash：#/bcc/list
     *          不同模块需要写path + hash，如bcc中跳转cdn: /cdn/#/cdn/domain/list
     *      框架处理：根据`WITH_ORG`或者`WITH_PRO`做页面跳转
     *  3. 自定义处理：
     *      适用场景：业务需要刷新部分数据或做一些特殊处理
     *      业务处理：在页面组件中设置`onOrgChange`方法，在方法中做处理
     *      框架处理：调用业务方onOrgChange
     */
    WITH_ORG?: boolean | string = true;
    WITH_PRO?: boolean | string = true;

    $redirect = redirect;

    /**
     * 当前页面刷新，可自定义reload方法，用于覆盖默认行为（router.locator.reload）
     */
    $reload() {
        this.reload ? this.reload() : router.locator.reload();
    }

    onOrgChange() {
        if (typeof this.WITH_ORG === 'string') {
            redirect(this.WITH_ORG);
        } else if (typeof this.WITH_PRO === 'string') {
            redirect(this.WITH_PRO);
        } else if (this.WITH_ORG || this.WITH_PRO) {
            const refresh = this.reload || this.refresh || this.attached || this.inited;
            refresh && refresh.call(this);
        }
    }
}

export abstract class ListPage extends BasePage {
    static [ListPageCatetory] = true;
}

export abstract class DetailPage extends BasePage {
    static [DetailPageCatetory] = true;
}

export abstract class CreatePage extends BasePage {
    static [CreatePageCategory] = true;
}

export type PageWapper = ListPage | DetailPage | CreatePage;
