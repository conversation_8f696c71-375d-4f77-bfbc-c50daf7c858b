/**
 * @Author:
 * @Date: 2022-03-14 17:03:17
 * @Description: 解析template模块
 */

export interface IRoute {
    /**
     * 路由的名称
     */
    name?: string;

    /**
     * 路由调整的目标地址
     */
    to?: string;

    /**
     * 菜单icon名称
     */
    icon?: string;

    /**
     * 是否是导航菜单
     */
    isMenu?: boolean;

    /**
     * 动态导航菜单对应的数据名称
     */
    sFor?: string;

    /**
     * 菜单显隐字段名
     */
    sIf?: string;

    /**
     * 子菜单
     */
    children?: Array<IRoute>;
}

/**
 * @description:
 * @param {Array} routes 路由列表
 * @param {IRoute} route 单个路由
 * @return {**} boolean
 */
function isOwnerRoute(routes: Array<IRoute> = [], route: IRoute): boolean {
    for (let i = 0; i < routes.length; i++) {
        if (routes[i]?.name === route?.name) {
            return true;
        }
    }
    return false;
}

// 初始化 pagemaker 的外部界面
if (window.amis) {
    if (!window.amis.external) {
        window.amis.external = {};
    }
    window.amis.external.pages = [];
}

/**
 * @description: 收集as-sidebar和as-page注册的所有路由
 * @param {**} getRoute：收集路由方法，
 * @return {**}
 */
export function registerRoutes(routes: Array<IRoute>) {
    const externalRoutes = window.amis?.external?.pages;
    if (!Array.isArray(routes)) {
        routes = [routes];
    }
    routes.forEach((route: IRoute) => {
        if (!route.name || !isOwnerRoute(externalRoutes, route)) {
            externalRoutes.push(route);
        }
    });
}

/**
 * @description: 收集asPage路由
 * @param {Array} routes ['/tms/resource/detail']
 * @return {**} [{linkTo: '/tms/resource/detail', name: 'tms_resource_detail'}]
 */
export function collectRoutes(routes: Array<string>) {
    routes.forEach(item => {
        if (item) {
            const route = {
                linkTo: item
            };
            route.name = item.replace(/\//g, '_').replace(/_/, '') || 'root';
            registerRoutes([route]);
        }
    });
}
