/**
 * runtime - Types
 *
 * @file types.js
 * <AUTHOR>
 */

import {SanComponent} from 'san';

export const RouterKey = Symbol.for('RouterKey');

export const SidebarKey = Symbol.for('SidebarKey');
export const DefaultSidebarName = Symbol.for('default-sidebar');
export const SidebarCategory = Symbol.for('SidebarCategory');

export const PageCatetory = Symbol.for('PageCatetory');
export const isPage = (Klass: SanComponent<any>) => !!Reflect.getMetadata(PageCatetory, Klass);

export const ListPageCatetory = Symbol.for('ListPageCatetory');
export const DetailPageCatetory = Symbol.for('DetailPageCatetory');
export const CreatePageCategory = Symbol.for('CreatePageCategory');

export const ServiceKey = Symbol.for('ServiceKey');
export const ServiceCategory = Symbol.for('ServiceCategory');
export const isService = (Klass: any) => !!Reflect.getMetadata(ServiceCategory, Klass);

export const CmptKey = Symbol.for('CmptKey');
export const CmptCategory = Symbol.for('CmptCategory');
export const isComponent = (Klass: any) => !!Reflect.getMetadata(CmptCategory, Klass);

export const PrepareKey = Symbol.for('PrepareKey');
export const PrepareDataKey = Symbol.for('PrepareDataKey');
