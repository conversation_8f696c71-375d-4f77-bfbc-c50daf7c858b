/**
 * utils
 *
 * @file utils.js
 * <AUTHOR>
 */

import _ from 'lodash';
import { router } from 'san-router';

export const isDevelopment = process.env.NODE_DEV !== 'production';

export const isSanCmpt = (Klass: any) =>
    Klass && Klass.prototype && (Klass.prototype.nodeType === 5 || Klass.prototype._type === 'san-cmpt');

export const isString = (typeName: any) => typeof typeName === 'string';

export const isFunciton = (typeName: any): typeName is Function => typeof typeName === 'function';

export const isPromise = (typeName: any) => isFunciton(typeName && typeName.then);

export const isObject = (typeName: any) => Object.prototype.toString.call(typeName) === '[object Object]';

export const isNumber = (typeName: any) => typeof typeName === 'number';

export const debounce = _.debounce;

export const html = String.raw;

export const preventDefault = (e: Event) => {
    if (e.preventDefault) {
        e.preventDefault();
    } else {
        e.returnValue = false;
    }
};

export const stopPropagation = (e: Event) => {
    if (e.stopPropagation) {
        e.stopPropagation();
    } else {
        e.cancelBubble = true;
    }
};

export const serializeQuery = (query: { [propKey: string]: string | object[] }) => {
    if (!query) {
        return '';
    }

    let search = '';
    for (const key in query) {
        if (query.hasOwnProperty(key)) {
            const value = query[key];

            // 如果`value`是数组，其`toString`会自动转为逗号分隔的字符串
            search += '&' + encodeURIComponent(key) + '=' + encodeURIComponent(value.toString());
        }
    }

    return search.slice(1);
};

/**
 * 重定向
 * @param {string|{module: string, path: string, paramSeperator?: string, params?:{[propKey: string]: string|object[]}}} url
 * @param option
 */
export const redirect = (
    url:
        | string
        | { module: string; path: string; paramSeperator?: string; params?: { [propKey: string]: string | object[] } },
    option?: object
) => {
    let newUrl = '';
    if (typeof url === 'object') {
        if (url.module) {
            newUrl += `/${url.module}/`;
        }
        newUrl += `#${url.path}`;
        if (url.params) {
            newUrl += `${url.paramSeperator || '?'}${serializeQuery(url.params)}`;
        }
    } else if (isString(url)) {
        newUrl = url;
    }
    if (/^#/.test(newUrl)) {
        router.locator.redirect(newUrl.replace(/^#/, ''), option);
    } else {
        window.location.href = newUrl;
    }
};

export const once = function (fn: Function) {
    let called = false;

    return function (this: any, name: string) {
        if (!called) {
            called = true;
            return fn.call(this, name);
        }
    };
};

/**
 * @description: 获取关键字
 * @param {**} str v-For
 * @return {**} vFor
 */
 export function lowerCamelCase(str: string = ''): string {
    const nameReg = /-(\w+)/g;
    if (nameReg.test(str)) {
        return str = str.replace(nameReg,function($,$1){
            return $1.slice(0, 1).toLocaleUpperCase() + $1.slice(1);
        });
    };
    return str;
}

/**
 * @description: 转化成模板字符串
 * @param {**} val 'this is my {{name}}'
 * @return {**} 'this is my ${name}}'
 */
 export function getVariable(val: string): string {
    const variableReg = /\{\{(\S+?)\}\}/g
    return val.replace(variableReg, function($, $1) {
        return '${' + $1 + '}';
    });
}


/**
 * 判断当前环境是否为 pagemaker 发布态
 */
export function isPagemakerReleaseState(): boolean {
    const appName = location.pathname.replace(/^\//, '').replace(/\//g, '_').toUpperCase();
    const isLocalMixApp = sessionStorage.getItem('LOCAL_MIX_PAGEMAKER_' + appName);
    const pagemakerApp = !!(sessionStorage.getItem(`PAGEMAKER_RELEASE_STATE_${appName}`) || (window as any).PAGEMAKER_APP_INFO);
    return pagemakerApp && !isLocalMixApp;
}
