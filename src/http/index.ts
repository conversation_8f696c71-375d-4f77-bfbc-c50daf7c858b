/**
 * 为业务提供HttpClient
 * @file httpclient.ts
 * <AUTHOR>
 */

import HttpClient from '@baiducloud/httpclient';
import { ServiceFactory } from '../factory';

export default class extends HttpClient {
    constructor(config: object) {
        super(config, ServiceFactory.resolve('$context'));
        // console.error(
        //     `%c💥 Httpclient已从@baiducloud/runtime中废弃，请直接使用'@baiducloud/httpclient，参考ChangeLog'\``,
        //     'color: red'
        // );
    }
}
