/**
 * Runtime
 *
 * @file index.js
 * <AUTHOR>
 */

import 'core-js/stable';

// 修复官方reflect-metadata与mobx中的inject在同一环境中运行异常
// 具体见官网issue：https://github.com/mobxjs/mobx-react/issues/756
import '@baidu/reflect-metadata';
import 'regenerator-runtime/runtime';

import { router } from 'san-router';

import * as decorators from './decorators';

import HttpClient from './http';
import PerfLog from './perflog';
import * as templates from './templates';
import Processor from './runtime/processor';
import { isDevelopment, html, redirect, isSanCmpt, isPagemakerReleaseState } from './helpers/utils';
import './styles/global.less';

import Perf from './perflog';

export * from './services';
export * from './factory';
export * from './helpers/interfaces';
export { default as Enum } from './helpers/enum';
export { AppView } from './app-view';

export { templates, decorators, html, redirect, isSanCmpt, HttpClient, Processor };

export const kCdnEndpoinit = 'https://bd.bcestatic.com/console/static';
export const kAppName = window.location.pathname.replace(/\//g, '') || 'home';

const kSearchArgs = window.location.search.substring(1).split('&');
const kDefaultEnvName = /^console\.bce\.baidu\.com/.test(window.location.hostname) ? 'online' : 'qasandbox';
const kCustomEnvName = kSearchArgs.find(args => /^env=/.test(args));

export const kEnvName = kCustomEnvName ? kCustomEnvName.replace(/^env=/, '') : kDefaultEnvName;
export const kAppBaseUrl = isDevelopment ? '/' : [kCdnEndpoinit, kAppName, kEnvName].join('/');

window.__webpack_public_path__ = kAppBaseUrl;

export function start() {
    if (!router.isStarted) {
        if (window.$framework) {
            window.$framework.events.on(window.$framework.EVENTS.AFTER_REGION_CHANGED, () => {
                const cmpt = router.routeAlives[0];
                const bizCmpt = cmpt.component.refBizApp();

                if (bizCmpt && bizCmpt.onRegionChange) {
                    bizCmpt.onRegionChange(window.$framework.region.getCurrentRegion());
                }
            });

            if (window.$framework.EVENTS.ORGANIZATION_CHANGED) {
                window.$framework.events.on(window.$framework.EVENTS.ORGANIZATION_CHANGED, (data: { data: { type: any; }; }) => {
                    const cmpt = router.routeAlives[0];
                    const bizCmpt = cmpt.component.refBizApp();

                    if (bizCmpt && bizCmpt.onOrgChange) {
                        const param = {
                            type: data.data.type,
                            organizationId: window.$framework.organization.getCurrentOrganization().id,
                            projectId: window.$framework.organization.getCurrentResourceGroup().id
                        };
                        bizCmpt.onOrgChange(param);
                    }
                });
            }
        }

        PerfLog.markTime('runtime-started');

        router.start();
    }
    router.listen((e: any) => {
        if (!window.amis && !isPagemakerReleaseState() && !e?.config) {
            const event = new CustomEvent('pageNotFound', {
                detail: e?.path
            });
            window.dispatchEvent(event);
        }
    })
}

export function initPerf() {
    Perf.init();
}
