/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file index.ts
 * <AUTHOR> (<EMAIL>)
 */

let markerIndex = 0;
const kUAVerison = navigator.userAgent.match(/Chrom(e|ium)\/([0-9]+)\./);
// chrome >= 78 & 开启debug开关
const kEnabledPerformaceLog = kUAVerison && parseInt(kUAVerison[2], 10) >= 78 && !!localStorage.getItem('debug');

// chrome >= 78开启上报开关
const kEnabledPerformaceSend = kUAVerison && parseInt(kUAVerison[2], 10) >= 78;

const kColorGray = 'color: #999';
const kColorGreen = 'color: green;';
const kColorLightGreen = 'color: #1DA653';
const kColorBlue = 'color: #108cee';

// 访问console模块的时间
const time = Date.now() - performance.now();

export default class PerfLog {
    // 当前页面url
    static url: string;
    static isPerfInit: boolean;

    static init() {
        PerfLog.isPerfInit = true;
    }

    static log() {
        const resources = performance.getEntriesByType('mark') as Array<PerformanceEntry & {detail: string}>;
        const [navigationTiming] = performance.getEntriesByType('navigation') as Array<PerformanceNavigationTiming>;

        resources.slice(markerIndex).forEach((item, index) => {
            if (item.name === 'runtime-started') {
                // TODO(zhanghao25): requestStart不太合适，需要细化
                const duration = (item.startTime - navigationTiming.requestStart).toFixed(2);

                console.groupCollapsed(`%c📌runtime-started %c+${duration}ms`, kColorGreen, kColorBlue);
                PerfLog.resourceInfo(navigationTiming.requestStart, item.startTime);
                console.info(item);
                console.groupEnd();

                return;
            }

            if (item.name === 'san-compiled') {
                console.group(`%c📌@page(${item.detail})`, kColorGreen);
            }

            const currentTime = item.startTime;
            const prevMarkerTime = resources[markerIndex + index - 1].startTime;

            PerfLog.resourceInfo(prevMarkerTime, currentTime);

            console.log(
                `⌛ %c${item.startTime.toFixed(2)} %c${item.name} %c+${(currentTime - prevMarkerTime).toFixed(2)}ms`,
                kColorGray, kColorLightGreen, kColorBlue
            )

            if (item.name === 'san-disposed') {
                console.groupEnd();
            }
        });

        markerIndex = resources.length;
    }

    static resourceInfo(prevMarkerTime: number, currentTime: number) {
        const resources = (performance.getEntriesByType('resource') as Array<PerformanceResourceTiming>)
            .filter(x => (x.responseEnd < currentTime, x.responseEnd > prevMarkerTime)) ;

        if (resources.length > 0) {
            console.groupCollapsed(`%c🔗resource-timing`, kColorGreen);
            resources.forEach(item => {
                const label = `${item.initiatorType}(${new URL(item.name).pathname})`;
                const responseTime = item.responseEnd.toFixed(2);
                const duration = item.duration.toFixed(2);

                console.groupCollapsed(
                    `⌛ %c${responseTime} %c${label} %c+${duration}ms`,
                    kColorGray, kColorLightGreen, kColorBlue
                );

                console.info(item);
                console.groupEnd();
            });
            console.groupEnd();
        }
    }

    static markLifeCircle(label: string, option?: any): MethodDecorator {
        if (!(kEnabledPerformaceLog || kEnabledPerformaceSend)) {
            return (target, propertyKey, descriptor) => descriptor;
        }

        return <Function>(target: Object, propertyKey: string | symbol, descriptor: TypedPropertyDescriptor<any>) => {
            const method = descriptor.value;

            if (performance) {
                descriptor.value = function (...args: any[]) {
                    (performance.mark as (markName: string, option?: any) => void)(label, option);
                    if (kEnabledPerformaceLog) {
                        PerfLog.log();
                    }

                    // 组件attached获取页面url
                    if (kEnabledPerformaceSend && (label === 'san-attached')) {
                        PerfLog.url = window.location.href;
                    }

                    // 开启上报开关 & san-disposed时上报
                    if (kEnabledPerformaceSend && (label === 'san-disposed')
                        && PerfLog.isPerfInit) {
                        PerfLog.sendLog();
                    }

                    return method.call(this, ...args);
                }
            }

            return descriptor;
        };
    };

    static markTime(label: string) {
        if (!(kEnabledPerformaceLog || kEnabledPerformaceSend)) {
            return;
        }

        performance.mark(label);
    }

    static sendLog() {
        const data = performance.getEntries();
        
        const params = {
            url: PerfLog.url,
            userAgent: navigator.userAgent,
            time,
            data
        };

        fetch('/api/perf/v1/save', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify(params)
        });

        // 清除上报过的mark和resource
        performance.clearMarks();
        performance.clearResourceTimings();
    }
}
