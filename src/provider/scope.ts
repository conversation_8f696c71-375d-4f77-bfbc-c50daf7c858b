/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file scope.ts
 * <AUTHOR> (<EMAIL>)
 */

import { ComponentConstructor, SanComponent } from 'san/types';
import { CmptCategory, ServiceCategory } from '../helpers/types';

export default class ScopeProvider {
    static ALL = 0;

    static containCmpt(Klass: ComponentConstructor<any, any>) {
        const { scope } = Reflect.getMetadata(CmptCategory, Klass);
        const $scope = new ScopeProvider();

        return $scope.contains(scope);
    }

    static priorityCmpt(LeftKlass: any, RightKlass: any): number {
        const leftmeta = Reflect.getMetadata(CmptCategory, LeftKlass);
        const rightmeta = Reflect.getMetadata(CmptCategory, RightKlass);

        return leftmeta.scope > rightmeta.scope ? 1 : -1;
    }

    static containService(Klass: any) {
        const { scope } = Reflect.getMetadata(ServiceCategory, Klass);
        const $scope = new ScopeProvider();

        return $scope.contains(scope);
    }

    static priorityService(LeftKlass: any, RightKlass: any): number {
        const leftmeta = Reflect.getMetadata(ServiceCategory, LeftKlass);
        const rightmeta = Reflect.getMetadata(ServiceCategory, RightKlass);

        return leftmeta.scope > rightmeta.scope ? 1 : -1;
    }

    contains(scope: number = ScopeProvider.ALL) {
        const currentScope = this.getCurrentScope();

        if (scope === ScopeProvider.ALL) {
            return true;
        } else if (currentScope === ScopeProvider.ALL) {
            return false;
        }

        return (scope & currentScope) === currentScope;
    }

    getCurrentScope() {
        return ScopeProvider.ALL;
    }
}
