/**
 * Azeroth - Processor
 *
 * @file Processor.js
 * <AUTHOR>
 */

import { ComponentConstructor } from 'san/types';
import { CmptFactory } from '../factory';
import { isPage, isComponent } from '../helpers/types';

export default class Processor {
    /**
     * 统一装配
     *
     * @param {Component} Klass
     */
    static autowire(Klass: any): ComponentConstructor<any, any> {
        return Processor.autowirePage(Processor.autowireComponent(Processor.autowireService(Klass)));
    }

    static autowireUnCheckCmpt(Klass: any): ComponentConstructor<any, any> {
        return Processor.autowirePage(Processor.autowireComponent(Processor.autowireService(Klass), true));
    }

    /**
     * 装配所有服务
     *
     * @param {Component} Klass
     */
    static autowireService(Klass: any) {
        return Klass;
    }

    /**
     * 装配所有组件
     *
     * @param {Component} Klass
     */
    static autowireComponent(Klass: any, unCheck: boolean = false) {
        const AutoWiredKey = Symbol.for('CMPT_AUTOWIRED_KEY');

        const stack = [Klass];
        while (stack.length > 0) {
            const Cmpt = stack.shift();
            if (Cmpt && !Cmpt[AutoWiredKey] && (unCheck ? true : (isPage(Cmpt) || isComponent(Cmpt)))) {
                Cmpt[AutoWiredKey] = true;

                const cmpts = CmptFactory.resloveCmpt(Cmpt);
                if (Object.keys(cmpts).length > 0) {
                    Cmpt.components = Object.assign({}, cmpts, Cmpt.components);

                    stack.unshift(...Object.values(Cmpt.components));
                }
            }
        }

        return Klass;
    }

    /**
     * 装配所有页面
     *
     * @param {Component} Klass
     */
    static autowirePage(Klass: any) {
        return Klass;
    }
}
