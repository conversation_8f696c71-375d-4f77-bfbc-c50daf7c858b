/**
 * 管理framework中的一些公共数据，作为app page的上下文，提供了Region、User等操作
 * 不建议直接使用init的几个参数，要用什么就加一个获取方法
 *
 * @file context.ts
 * <AUTHOR>
 */

import u from 'lodash';

import { Enum } from '../helpers';
import defaultEnums from './enums';
import { asService } from '../decorators';
import ServiceFactory from '../factory/service-factory';

let K: any = {};
let Flags: any = {};
let User: any = {};

// 全局维护一下事件，因为context在每个组件里面都会被初始化一个实例
const EVENTS: { [propName: string]: string } = {
    REGION_CHANGED: 'rc'
};

const Listeners: { [propName: string]: (() => void)[] } = {
    [EVENTS.REGION_CHANGED]: []
};

@asService('$context')
export class ContextService {
    // 只在framework初始化后回传参数使用，不允许设置，设置可调用framework中的方法
    init = u.once((context: FrameworkContext) => {
        u.extend(K, context.constants);
        u.extend(Flags, context.flags);
        u.extend(User, context.session);
    });

    // 监听事件类型
    EVENTS = EVENTS;

    /**
     * 添加监听方法
     *
     * @param type {string} 触发事件类型
     * @param listener {function} 触发事件
     */
    on(type: string, listener: () => void) {
        if (!type || !listener) {
            return;
        }
        const listenList = Listeners[type];
        if (listenList && !~listenList.indexOf(listener)) {
            listenList.push(listener);
        }
    }

    /**
     * 移除监听方法
     *
     * @param type {string} 触发事件类型
     * @param listener {function} 触发事件
     */
    un(type: string, listener: () => void) {
        if (!type || !listener) {
            return;
        }
        const listenList = Listeners[type];
        if (!listenList) {
            return;
        }
        const index = listenList.indexOf(listener);
        ~index && listenList.splice(index, 1);
    }

    /**
     * 触发监听方法
     *
     * @param type {string} 触发事件类型
     */
    fire(type: string) {
        if (!type) {
            return;
        }
        const listenList = Listeners[type];
        listenList.forEach(listener => {
            listener();
        });
    }

    get OrderSuccessUrl() {
        return K.orderSuccessUrl || {};
    }

    /**
     * region/available接口返回的region信息
     *
     * @readonly
     * @memberof ContextService
     * @return {Array}
     */
    get RegionAvailable() {
        return K.regionAvailable || [];
    }

    getOrderSuccessUrl() {
        return K.orderSuccessUrl || {};
    }

    getCurrentRegion() {
        return window?.$framework.region.getCurrentRegion();
    }

    /**
     * 切换region
     *
     * @param regionId {string} region id
     */
    setRegion(regionId: string) {
        return window?.$framework.region.setRegion(regionId);
    }

    getCurrentRegionId() {
        return this.getCurrentRegion().id;
    }

    get SERVICE_TYPE() {
        return K.SERVICE_TYPE;
    }

    get currentLanguage() {
        try {
            return window?.$framework.i18n.getCurrentLanguage();
        } catch (e) {
            return 'zh-cn';
        }
    }

    getEnum(enumKey: keyof typeof defaultEnums) {
        if (!enumKey || !defaultEnums[enumKey]) {
            return null;
        }

        const AllRegion = window?.$framework?.region?.AllRegion;

        if (enumKey === 'AllRegion' && AllRegion) {
            return AllRegion;
        }

        return new Enum(...defaultEnums[enumKey]);
    }

    get UserId(): string {
        return K && K.userid;
    }
    // 不要再写这种写法了
    getUserId() {
        return K && K.userid;
    }

    /**
     * 判断当前用户是否是企业用户
     *
     * @return {boolean}
     */
    isEnterprise() {
        return K && K.verifyUser && K.verifyUser.qualifyType === 'ENTERPRISE';
    }

    /**
     * 判断当前用户是否实名认证
     *
     * @return {boolean}
     */
    isVerifyUser() {
        return K && K.verifyUser && K.verifyUser.verifyStatus === 'PASS';
    }

    /**
     * 因用户恶意操作的封禁检查
     *
     * @param {string} serviceName 服务名称
     * @return {boolean} 是否封禁
     * @memberof Context
     */
    checkForbidden(serviceName: string): boolean {
        return !!(K && K.forbiddenService && K.forbiddenService.split(',').indexOf(serviceName) !== -1);
    }

    /**
     * 当前是否是线上环境
     *
     * @return {boolean}
     */
    isOnline() {
        return Flags['is_online'] && location.host.indexOf('qasandbox') === -1;
    }

    /**
     * 获取当前
     */
    getCsrfToken() {
        const $cookie = ServiceFactory.resolve('$cookie');
        return $cookie.get('bce-user-info');
    }

    /**
     * 是否主账号代理子账号登录
     */
    isSubUser() {
        const $cookie = ServiceFactory.resolve('$cookie');
        return !!$cookie.get('bce-subuser-info');
    }

    /**
     * 判断用户是否已开通某个服务
     * @param service 服务serviceName
     */
    isServiceActive(service: string) {
        return !!(K && K.activeService.split(',').includes(service));
    }

    /**
     * 获取publicKey
     */
    getPublicKey() {
        return K && K.publicKey;
    }

    /**
     * 获取登录页面url
     */
    getLogoutUrl() {
        return K && K.logoutUrl;
    }

    /**
     * 获取备案、工单、控制台页面url
     */
    get Domains() {
        return {
            ...(K?.domains || []),
            portal: window?.$framework?.project?.CUR_PORTAL_LINK
        };
    }
    // 保留一下，兼容以前老的用法
    getDomains() {
        return {
            ...(K?.domains || []),
            portal: window?.$framework?.project?.CUR_PORTAL_LINK
        };
    }

    /** 项目名称，例如百度智能云、企业云等 */
    get ProjectName(): string {
        return window?.$framework?.project?.PROJECT_NAME;
    }

    /**
     * 获取当前项目环境下用户已开通的服务
     */
    getAvailableService() {
        return K && K.availableService;
    }

    /**
     * seniorPassFlag
     */
    getSeniorPassFlag(): string {
        return K?.verifyUser?.seniorPassFlag;
    }
}
