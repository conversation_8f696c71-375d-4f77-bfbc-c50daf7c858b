/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file doc.ts
 * <AUTHOR> (<EMAIL>)
 */

import { asService } from '../decorators';

type DocLinks = {
    [key: string]: string;
};

@asService('$doc')
export class DocService {
    constructor(docLinks: DocLinks) {
        const links = docLinks || {};

        Object.entries(links).forEach(([key, value]) => {
            Object.defineProperty(this, key, {
                get(): string {
                    const parser = window.$framework?.project?.parseDocLink;
                    const portal = window.$framework?.project?.CUR_PORTAL_LINK;
                    const link = portal + value;

                    return parser ? parser(link) : link;
                }
            });
        });
    }
}
