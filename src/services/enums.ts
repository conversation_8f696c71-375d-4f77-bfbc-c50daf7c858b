/**
* 存放一些默认的配置数据
* @file helpers/config.ts
* <AUTHOR>
*/

export default {
    Payment: [
        {alias: 'PREPAY', text: '预付费', value: 'Prepay'},
        {alias: 'POSTPAY', text: '后付费', value: 'Postpay'}
    ],
    AllRegion: [
        {alias: 'GLOBAL', text: '全局', value: 'global'},
        {alias: 'BJ', text: '华北 - 北京', value: 'bj'},
        {alias: 'CNN1', text: '华北 - 北京', value: 'cn-n1'},
        {alias: 'GZ', text: '华南 - 广州', value: 'gz'},
        {alias: 'HK', text: '中国香港1区', value: 'hk'},
        {alias: 'HKG', text: '中国香港', value: 'hkg'},
        {alias: 'HK02', text: '中国香港2区', value: 'hk02'},
        {alias: 'SU', text: '华东 - 苏州', value: 'su'},
        {alias: 'FSH', text: '华东 - 上海', value: 'fsh'},
        {alias: 'BD', text: '华北 - 保定', value: 'bd'},
        {alias: 'HBFSG', text: '华北 - 度小满金融专区', value: 'hb-fsg'},
        {alias: 'FWH', text: '金融华中 - 武汉', value: 'fwh'},
        {alias: 'SIN', text: '新加坡', value: 'sin'},
        {alias: 'BJFSG', text: '华北 - 度小满金融专区二', value: 'bjfsg'},
        {alias: 'SZFSG', text: '华东 - 度小满金融专区', value: 'szfsg'},
        {alias: 'GZFSG', text: '华南 - 度小满金融专区', value: 'gzfsg'},
        {alias: 'BJKS', text: '华北 - 快手专区', value: 'bjks'},
        {alias: 'HZ', text: '杭州', value: 'hz'},
        {alias: 'CNHZPRO', text: '中国（杭州）', value: 'cnhzpro'},
        {alias: 'ABCSTACKHZ', text: '私有云杭州', value: 'abcstack_hz'},
        {alias: 'HB', text: '华北', value: 'hb'},
        {alias: 'CNJD', text: '中国（上海）', value: 'cnjd'},
        {alias: 'P3', text: '深圳3号', value: 'p3'},
        {alias: 'P2', text: '上海2号', value: 'p2'},
        {alias: 'WH', text: '华中-武汉', value: 'wh'}
    ]
};
