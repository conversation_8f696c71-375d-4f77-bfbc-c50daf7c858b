/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file flag.ts
 * <AUTHOR> (<EMAIL>)
 */

import _ from "lodash";
import { asService } from '../decorators';
import { isPagemakerReleaseState } from '../helpers/utils';

@asService('$flag')
export class FlagService {
    [x: string]: boolean | string | Function;

    template: string;

    constructor(remoteFlags: string[] = [], template: string) {
        const localFlags = sessionStorage.getItem('__FLAGS');
        if ((window.amis || isPagemakerReleaseState()) && !_.isEmpty((window as any)?.store?.variable.appVariables.FLAG)) {
            remoteFlags = Object.keys((window as any)?.store?.variable?.appVariables.FLAG || []);
        }
        const flags = localFlags ? JSON.parse(localFlags) : remoteFlags;

        this.template = template;
        flags.forEach((x: string | number) => (this[x] = true));
    }

    is(template: string): boolean {
        return this.template === template;
    }

    some(...names: string[]): boolean {
        return names.some(x => this[x]);
    }

    every(...names: string[]): boolean {
        return names.every(x => this[x]);
    }
}
