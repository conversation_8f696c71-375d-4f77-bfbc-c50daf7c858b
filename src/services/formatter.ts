/**
 * @file services/formatter.es6
 * <AUTHOR>
 */

import u from 'lodash';
import moment from 'moment';
import { asService } from '../decorators';

const kByteUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB', 'BB'];
const kBitUnit = ['', 'k', 'm', 'g', 't', 'p', 'e', 'z', 'y', 'b'];

@asService('$formatter')
export class FormatterService {
    isByteUnit(value: string) {
        return u.indexOf(kByteUnit, value) !== -1;
    }

    percent(value: string) {
        return value + '%';
    }

    bytes(value: number, number = 2, byteUnit = 1024, unit: string) {
        let idx = unit ? u.indexOf(kByteUnit, unit) : 0;
        let len = kByteUnit.length - 1;
        while (value >= byteUnit && idx < len) {
            value = value / byteUnit;
            idx++;
        }
        return value.toFixed(number) + kByteUnit[idx];
    }

    bits(value: number, number = 2, bitUnit = 1024) {
        let idx = 0;
        let len = kBitUnit.length - 1;
        while (value >= bitUnit && idx < len) {
            value = value / bitUnit;
            idx++;
        }
        return value.toFixed(number) + kBitUnit[idx];
    }

    number(value: number, number = 1) {
        if (value < 10000) {
            return value;
        }
        // 15000、20000，当number为0的时候，都是2万
        // 所以需要判断一下value是否能整除，不能整除的至少保留一位小数
        else if (value < 1000000) {
            return (value / 10000).toFixed(value % 10000 === 0 ? number : Math.max(1, number)) + '万';
        } else if (value < 10000000) {
            return (value / 1000000).toFixed(value % 1000000 === 0 ? number : Math.max(1, number)) + '百万';
        }
        return (value / 10000000.0).toFixed(value % 10000000 === 0 ? number : Math.max(1, number)) + '千万';
    }

    time(value: string, formatter = 'YYYY-MM-DD HH:mm:ss') {
        return moment(value).format(formatter);
    }
}
