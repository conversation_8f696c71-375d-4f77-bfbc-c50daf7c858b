/**
 * @file 基于 window.name 的 storage 模块
 * 支持 JSON 格式的数据存储
 *
 */
import { asService } from '../decorators';

const SCHEME = 'name-storage:';
const RE_PAIR = /^([^=]+)(?:=(.*))?$/;

interface Storage {
    [propName: string]: string;
}

@asService('$nameStorage')
export class NameStorage {
    public originName = '';
    private nameStorage: Storage = {};
    static instance: NameStorage;

    constructor() {
        if (NameStorage.instance) {
            return NameStorage.instance;
        } else {
            NameStorage.instance = this;
        }
        this.parse(window.name);
    }

    /**
     * 解析并初始化 window.name 数据
     * 标准的 nameStorage 数据格式为 `name-storage:origin-name?key=value`
     *
     * @param {string} window.name
     */
    private parse(name: string) {
        if (name && name.indexOf(SCHEME) === 0) {
            // 转化为 [ 'name-storage', 'origin-name', 'key=value' ] 形式
            const match = name.split(/[:?]/);
            this.originName = decodeURIComponent(match[1]);
            const params = match.slice(2).join('');
            const pairs = params.split('&');
            for (let i = 0, pair, key, value, l = pairs.length; i < l; i++) {
                pair = pairs[i].match(RE_PAIR);
                if (!pair || !pair[1]) {
                    continue;
                }

                key = decodeURIComponent(pair[1]);
                value = decodeURIComponent(pair[2]);

                this.nameStorage[key] = value;
            }
        } else {
            this.originName = name;
        }
    }

    /**
     * 修改一个字段的数据
     *
     * @param {string} key 键名
     * @param {*} value 键值
     */
    setItem(key: string, value: any) {
        if (typeof key !== 'string') {
            throw new Error('错误的nameStorage.setItem使用，非法键值');
        }
        this.nameStorage[key] = JSON.stringify(value);
        this.save();
    }

    /**
     * setItem 的别名
     */
    get set() {
        return this.setItem;
    }

    /**
     * 返回一个字段的数据
     *
     * @param {string} key 键名
     * @return {*} JSON parse后的数据。如果键名不存在于存储中，则返回 null。
     */
    getItem(key: string) {
        if (this.nameStorage.hasOwnProperty(key)) {
            let value = null;
            try {
                value = JSON.parse(this.nameStorage[key]);
            } catch (e) {}
            return value;
        } else {
            return null;
        }
    }

    /**
     * getItem 的别名
     */
    get get() {
        return this.getItem;
    }

    /**
     * 删除一个字段的数据
     *
     * @param {string} key 需要移除的键名
     */
    removeItem(key: string) {
        if (!this.nameStorage.hasOwnProperty(key)) {
            return;
        }
        delete this.nameStorage[key];
        this.save();
    }

    /**
     * removeItem 的别名
     */
    get remove() {
        return this.removeItem;
    }

    /**
     * 清空所有数据
     */
    clear() {
        this.nameStorage = {};
        this.save();
    }

    /**
     * 将数据保存到 window.name
     */
    private save() {
        const pairs = [];

        for (let [key, value] of Object.entries(this.nameStorage)) {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }

        window.name = !pairs.length
            ? this.originName
            : `${SCHEME}${encodeURIComponent(this.originName)}?${pairs.join('&')}`;
    }
}
