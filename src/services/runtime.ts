/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file runtime.ts
 * <AUTHOR> (<EMAIL>)
 */

import { asService } from '../decorators';
import CmptFactory from '../factory/cmpt-factory';
import ScopeProvider from '../provider/scope';
import { CmptCategory, CmptKey } from '../helpers/types';
import { ComponentConstructor } from 'san/types';
import PageFactory from '../factory/page-factory';

@asService('$runtime')
export class RuntimeService {
    private tableLog(datasource: [string, ComponentConstructor<any, any>[]][] = []) {
        const entries = datasource.map(([key, value]) => [
            key,
            Object.fromEntries(
                value.map(Klass => {
                    const metadata = Reflect.getMetadata(CmptCategory, Klass);
                    const scopeMatched = ScopeProvider.containCmpt(Klass) ? '✅' : '';
                    const scopeColumn = `${scopeMatched}Scope(${metadata.scope || '0'})`;

                    return [scopeColumn, JSON.stringify(metadata)];
                })
            )
        ]);
        console.table(Object.fromEntries(entries));
    }

    profile() {
        const cache = CmptFactory.getEntries();

        console.group(`%c📌@Component Profiles`, 'color: green');

        // runtime builtins components
        console.groupCollapsed(`%c📌Builtin components`, 'color: green');
        this.tableLog(Object.entries(cache).filter(x => /^@(app|xui|ui)/.test(x[0])));
        console.groupEnd();

        // biz components
        console.groupCollapsed(`%c📌Biz components`, 'color: green');
        this.tableLog(Object.entries(cache).filter(x => /^@(?!(app|xui|ui))/.test(x[0])));
        console.groupEnd();

        console.groupEnd();
    }

    export() {
        const pageEntries = PageFactory.getEntries();

        pageEntries.forEach((Klass, meta) => {
            console.groupCollapsed(`%c📌@page(${meta.name})${meta.desc ? ` | ${meta.desc}` : ''}`, 'color: green;');

            const metadata: {} = Reflect.getMetadata(CmptKey, Klass);
            this.tableLog(Object.values(metadata).map(x => [x, CmptFactory.resolve(x)]));

            console.groupEnd();
        });
    }
}
