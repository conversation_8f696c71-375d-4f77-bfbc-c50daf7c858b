/**
 * @file 本地存储 业务层封装，便于使用
 * <AUTHOR>
 */

import u from 'lodash';
import ServiceFactory from '../factory/service-factory';
import { asService } from '../decorators';
import { ContextService } from '@baiducloud/runtime/services/context';

interface Subject {
    getItem: (key: string) => any;
    setItem: (key: string, value: any) => any;
    removeItem: (key: string) => any;
}

@asService('$storage')
export class StorageService {

    /**
     * 调用方法集生成器，生成一套方法用于在指定的存储中使用特定的key生成一个存储空间，然后在这个存储空间中单独存储处理
     *
     * @param {Object} subject 目标存储
     * @param {Function} keyGenerator key生成方法
     * @constructor
     */
    constructor(subject?: Subject, keyGenerator?: Function) {
        // 目标存储
        subject && (this.subject = subject);
        keyGenerator && (this.keyGenerator = keyGenerator);
    }

    private subject: Subject = localStorage;

    private keyGenerator: Function = this.defaultKeyGenerator;

    /**
     * 获取业务环境内区分用户的key，使用此key作为存储的键值避免误覆盖，去获得一个存储的空间
     *
     * @return {string} 区分用户的key
     */
    private defaultKeyGenerator() {
        const $context = ServiceFactory.resolve('$context') as ContextService;
        // 本地存储的key使用用户id
        // userid 当前在constant接口中增加，以后没准换接口名
        return $context.getUserId() || 'default-user';
    }

    private getKey(): string {
        /* eslint-disable */
        if (!this.SPEC_KEY) {
            this.SPEC_KEY = this.keyGenerator();
        }
        return this.SPEC_KEY!;
        /* eslint-enable */
    }

    /**
     * 存储时对应的真正存储key
     * @type {?string}
     */
    SPEC_KEY = null;

    /**
     * 获取全部数据（拷贝）
     *
     * @return {Object}
     */
    dump(): { [key: string]: object } {
        let data = this.subject.getItem(this.getKey());
        try {
            if (typeof data === 'string') {
                data = JSON.parse(data);
            }
        } catch (e) {}

        if (!u.isObject(data)) {
            data = {};
        }
        return u.clone(data);
    }

    /**
     * 返回一个字段的数据
     *
     * @param {string} key 字段
     * @return {Object} 返回key所对应的值。
     */
    get(key: string): { [key: string]: any } {
        const data = this.dump();

        return u.clone(data[key]);
    }

    /**
     * 修改一个字段的数据
     *
     * @param {string} key 字段
     * @param {*} value 新的值
     */
    set(key: string, value: any) {
        const data = this.dump();

        data[key] = value;
        this.subject.setItem(this.getKey(), JSON.stringify(data));
    }

    /**
     * 清空
     */
    clear() {
        this.subject.removeItem(this.getKey());
    }

    /**
     * 删除一个字段的数据
     *
     * @param {string} key 字段
     */
    remove(key: string) {
        const data = this.dump();

        if (u.isObject(data) && data.hasOwnProperty(key)) {
            delete data[key];
        }
        this.subject.setItem(this.getKey(), JSON.stringify(data));
    }

    /**
     * 更新一个字段的值
     * key不存在则创建一个该字段的新数据
     * 如果都为Object，则做扩展处理
     * 否则替换处理
     *
     * @param {string} key 字段
     * @param {*} value 新的值
     */
    update(key: string, value: any) {
        const data = this.dump();

        if (data.hasOwnProperty(key) && u.isObject(data[key]) && u.isObject(value)) {
            data[key] = Object.assign(data[key], value);
        } else {
            data[key] = value;
        }
        this.subject.setItem(this.getKey(), JSON.stringify(data));
    }
}
