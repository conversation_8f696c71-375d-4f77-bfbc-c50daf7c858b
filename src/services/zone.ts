/**
 * @file services/zone.es6
 * <AUTHOR>
 */

import { asService } from '../decorators';

@asService('$zone')
export class ZoneService {
    getLabel(value: string = '-'): string {
        if (!value) {
            return '-';
        }

        // 如果value中有逗号，则为多可用区
        if (value.match(',')) {
            return '多可用区' + value.replace(/,/g, '+').replace(/zone/g, '');
        }
        return value.replace(/zone/g, '可用区');
    }
}
