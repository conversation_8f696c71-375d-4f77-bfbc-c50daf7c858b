@import "./x-rolling.less";
@import "./x-ui-fake.less";
@import "./util.less";
@import "./x-status.less";
@import "./x-skeleton.less";

body {
    font-size: 12px;
    line-height: 1.5em;
    color: #333;
    min-width: 1280px;
    background: #F5F5F5;
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro SC", "SF Pro Text", "Helvetica Neue", Helvetica, "PingFang SC", "Segoe UI", Roboto, "Hiragino Sans GB", "Arial", "microsoft yahei ui", "Microsoft YaHei", SimSun, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    display: flex;
    flex-flow: row nowrap;
    padding: 0;
    margin: 0;
}

a {
    text-decoration: none;
}

* {
    box-sizing: border-box;
}

// framework中logo样式不兼容
#root-app-container .logo {
    box-sizing: content-box;
}

#main {
    display: flex;
    flex: auto;
    height: calc(~"100vh - 50px");
    // background: url(../../assets/process.svg) center center no-repeat;
    background-size: 5%;
}

@keyframes animation-rolling_red {
    0% {
        left: 0;
    }

    50% {
        left: 13px;
    }

    100% {
        left: 0;
    }
}

@keyframes animation-rolling_blue {
    0% {
        left: 25px;
    }

    50% {
        left: 12px;
    }

    100% {
        left: 25px;
    }
}
