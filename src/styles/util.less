/* 这里写一些runtime内置的一些样式类名供开发人员直接使用吧 */

/* 生成内置的margin和padding */
.generate-pm(@n, @i: 0) when (@i =< @n) {
    .mt@{i} {
        margin-top: unit(@i, px);
    }

    .ml@{i} {
        margin-left: unit(@i, px);
    }

    .mr@{i} {
        margin-right: unit(@i, px);
    }

    .mb@{i} {
        margin-bottom: unit(@i, px);
    }

    .pt@{i} {
        padding-top: unit(@i, px);
    }

    .pl@{i} {
        padding-left: unit(@i, px);
    }

    .pr@{i} {
        padding-right: unit(@i, px);
    }

    .pb@{i} {
        padding-bottom: unit(@i, px);
    }

    .generate-pm(@n, (@i + 5));
}

.generate-pm(20); // 先只支持到20px吧

.no-padder {
    padding: 0;
}

.no-margin {
    margin: 0;
}

/* 提示文案 */
.grey-tip {
    color: #666;
    font-size: 12px;
}

.align-right {
    text-align: right;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.float-right {
    float: right;
}

.float-left {
    float: left;
}

.panel-loading {
    position: relative;
    filter: blur(2px);

    &:before {
        z-index: 1;
        content: '';
        height: 100%;
        width: 100%;
        position: absolute;
        background-color: rgba(255, 255, 255, 0.5);
        opacity: .8;
        background-image: url(https://bce.bdstatic.com/console/dist/009bbae/dep/inf-style/0.0.0/img/process.gif);
        background-repeat: no-repeat;
        background-position: center 30%;
    }
}

