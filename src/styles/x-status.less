/* 列表中的状态 */
.status {
    position: relative;
    margin-right: 5px;
    font-size: 12px;
    line-height: 16px;
    color: inherit;


    &:before {
        font-family: "iconfont";
        content: "\e632";
        font-size: inherit;
        position: relative;
        margin-right: 10px;
    }

    &.error:before {
        color: #EB5252;
    }

    &.normal:before {
        color: #2CB663;
    }

    &.waiting:before {
        color: #108cee;
        content: "\e6e9";
    }

    &.warning:before {
        color: #F4B329;
    }

    &.unavailable:before {
        color: #999;
    }

    &.rolling:before {
        font-family: "iconfont";
        content: "\e632";
        font-size: inherit;
        color: #F4B329;
        position: absolute;
        left: 0;
        top: 1px;
        margin-left: -27px;
        animation: animation-rolling_red 2s;
        animation-iteration-count: infinite;
    }

    &.rolling:after {
        font-family: "iconfont";
        content: "\e632";
        font-size: inherit;
        color: #108cee;
        position: absolute;
        left: 25px;
        top: 1px;
        margin-left: -39px;
        animation: animation-rolling_blue 2s;
        animation-iteration-count: infinite;
    }
}
