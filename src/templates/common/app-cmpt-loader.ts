/**
 * 动态组件加载器
 *
 * @file app-cmpt-loader.ts
 * <AUTHOR>
 */

import {Component} from 'san';

import {CmptFactory} from '../../factory';
import {isString, isFunciton, isPromise} from '../../helpers/utils';
import {asComponent} from '../../decorators';

@asComponent('@app-cmpt-loader')
class AppCmptLoader extends Component {
    static template = '<div></div>';

    attached() {
        this.load(this.data.get('target'));
    }

    attachAsyncComp(Comp) {
        if (this.cmpt) {
            if (this.cmpt instanceof Comp) {
                return;
            }
            this.cmpt.dispose();
            this.cmpt = null;
        }

        this.cmpt = new Comp({owner: this});
        this.cmpt.attach(this.el);
    }

    load(target) {
        if (isString(target)) {
            const ident = target.trim();

            if (/^@/.test(ident)) {
                this.attachAsyncComp(CmptFactory.resolve(ident));
            }

            // TODO: #/x//x/x/x
        }

        if (isPromise(target)) {
            return target.finally(Comp => this.attachAsyncComp(Comp));
        }

        if (isFunciton(target)) {
            this.attachAsyncComp(target);
        }
    }
};
