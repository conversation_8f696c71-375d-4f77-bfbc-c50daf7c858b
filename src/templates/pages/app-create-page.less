@import "../styles/mixins.less";

.app-create-page {
    width: 100%;
    background: #fff;
    overflow: auto;

    &-title {
        color: #333;
        font-size: 14px;
        display: flex;
        padding: 0 20px;
        line-height: 50px;
        position: relative;
        justify-content: center;
        border-bottom: 1px solid #ddd;

        .page-title-text {
            font-size: 16px;
            font-weight: bold;
        }

        .page-title-nav {
            left: 20px;
            display: inline-flex;
            color: currentColor;
            position: absolute;
        }

        .page-floating-nav {
            top: 70px;
            opacity: 0;
            left: 50px;
            display: flex;
            width: 50px;
            height: 50px;
            cursor: pointer;
            border-radius: 50px;
            position: fixed;
            align-items: center;
            justify-content: center;
            color: currentColor;
            background: #fff;
            transition: all .3s;
            box-shadow: 0 1px 10px 0 rgba(0,0,0,0.15);

            &:hover {
                color: #108cee;
            }

            &.ani-fade-out {
                opacity: 0;
                left: 50px;
            }

            &.ani-fade-in {
                opacity: 1;
                left: 80px;
            }
        }
    }

    &-content {
        display: flex;
        flex: auto;
        flex-flow: column;
        align-items: center;
        padding-bottom: 100px;

        &>.app-legend {
            width: 980px;
            margin-top: 20px;
            padding: 15px 20px;
            box-sizing: border-box;
            border: 1px solid #EBEBEB;

            &:last-child {
                flex: auto;
            }
        }
    }

    &-footer {
        bottom: 0;
        display: flex;
        position: fixed;
        height: 80px;
        width: 100%;
        background: #FFFFFF;
        justify-content: center;
        box-shadow: 0 1px 10px 0 rgba(0,0,0,0.10);
        z-index: 999;

        .page-footer-wrapper {
            width: 980px;
            padding: 20px 0;
            box-sizing: border-box;
        }
    }
}

// 处理侧边栏展开和缩起
.sidebar-expanded {
    .app-create-page-title .page-floating-nav.ani-fade-in {
        left: 200px;
    }

    .app-create-page-footer {
        width: ~'calc(100% - 180px)';
    }
}
