/**
 * app list page
 *
 * @file AppListPage.js
 * <AUTHOR>
 */

import {Button} from '@baiducloud/bce-ui/san';

import Component from './component';
import {asComponent, invokeCmpt} from '../../decorators';

import './app-create-page.less';
import {html, debounce} from '../../helpers/utils';

@asComponent('@app-create-page')
@invokeCmpt('@app-link')
export default class extends Component {
    static template = html`
        <div class="app-create-page">
            <div class="app-create-page-title">
                <app-link class="page-floating-nav {{computedKlass}}" to="{{backTo}}">
                    <i class="iconfont icon-arrow-left" />
                </app-link>
                <slot name="pageTitle">
                    <app-link class="page-title-nav" to="{{backTo}}">
                        <i class="iconfont icon-arrow-left" />
                        {{backTo<PERSON>abel}}
                    </app-link>
                    <span class="page-title-text">
                        {{pageTitle}}
                    </span>
                </slot>
            </div>
            <div class="app-create-page-content">
                <slot />
            </div>
            <div class="app-create-page-footer">
                <div class="page-footer-wrapper">
                    <slot name="pageFooter" />
                </div>
            </div>
        </div>
    `;

    static components = {
        'xui-button': Button
    };

    static computed = {
        computedKlass() {
            return this.data.get('offsetTop') > 60
                ? 'ani-fade-in'
                : 'ani-fade-out';
        }
    };

    initData() {
        return {
            pageTitle: '创建',
            backToLabel: '返回',
            backTo: this.owner.data.get('route').referrer
        }
    }

    _onScrollEvt = debounce(() => this.data.set('offsetTop', this.el.scrollTop), 100);

    attached() {
        this.el.addEventListener('scroll', this._onScrollEvt);
    }
}
