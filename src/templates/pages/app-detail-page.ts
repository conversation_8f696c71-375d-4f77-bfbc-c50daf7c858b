/**
 * app list page
 *
 * @file AppListPage.js
 * <AUTHOR>
 */

import {Button} from '@baiducloud/bce-ui/san';

import Component from './component';
import {asComponent, invokeCmpt} from '../../decorators';

import './app-detail-page.less';
import {html} from '../../helpers/utils';

@invokeCmpt('@app-link')
@asComponent('@app-detail-page')
export default class extends Component {
    static template = html`
        <div class="app-detail-page">
            <div class="app-detail-page-title">
                <slot name="pageTitle" >
                    <template san-if="{{backTo}}">
                        <app-link to="{{backUrl}}"><xui-button icon="arrow-left"/></app-link>
                        <span class="page-back-text">
                            {{backTo.text}}
                        </span>
                    </template>
                    <span class="page-title-text {{backTo ? 'center' : ''}}">
                        {{pageTitle}}
                    </span>
                </slot>
            </div>
            <div class="app-detail-page-content">
                <slot />
            </div>
        </div>
    `;

    static components = {
        'xui-button': Button
    };

    static computed = {
        backUrl() {
            const backTo = this.data.get('backTo');
            if (backTo) {
                const referrer = this.data.get('referrer');
                return backTo.url || referrer;
            }
            return '';
        }
    };

    initData() {
        return {
            referrer: this.owner.data.get('route').referrer
        };
    }
}
