/**
 * app list page
 *
 * @file AppListPage.js
 * <AUTHOR>
 */

import Component from './component';

import './app-list-page.less';
import {html} from '../../helpers/utils';
import {asComponent} from '../../decorators';

@asComponent('@app-list-page')
export default class AppListPage extends Component {
    static template = html`
        <div class="app-list-page">
            <div class="app-list-content">
                <slot name="pageTitle"><h2 san-if="pageTitle">{{pageTitle}}</h2></slot>
                <div class="table-full-wrap">
                    <div class="operation-wrap" san-if="uiShowOperation">
                        <div class="buttons-wrap" san-if="uiShowBulk"><slot name="bulk"/></div>
                        <div class="buttons-quick-wrap" san-if="uiShowFilter"><slot name="filter"/></div>
                    </div>
                    <slot/>
                    <div class="foot-pager">
                        <slot name="pager" />
                    </div>
                </div>
            </div>
        </div>
    `;

    static computed = {
        uiShowOperation() {
            const uiShowBulk = this.data.get('uiShowBulk');
            const uiShowFilter = this.data.get('uiShowFilter');

            return uiShowBulk || uiShowFilter;
        }
    }

    initData() {
        return {
            uiShowBulk: true,
            uiShowFilter: true
        };
    }

    inited() {
        if (this.sourceSlots) {
            const {bulk, filter} = this.sourceSlots.named;
            this.data.set('uiShowBulk', !!bulk);
            this.data.set('uiShowFilter', !!filter);
        }
    }
}
