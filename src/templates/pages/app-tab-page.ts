/**
 * tab形式的页面，暂时只支持当前产品内的页面放到一个tab，即Content的url必须是hash部分
 * 用法：
 * <app-tab-page>
 *     <app-tab-panel url="#/cdn/detail/origin" title="源站信息">
 *         内容
 *     </app-tab-panel>
 *     <app-tab-panel url="#/cdn/detail/https" title="https配置">
 *         内容
 *     </app-tab-panel>
 * </app-tab-page>
 *
 * @file AppTabPage.js
 * <AUTHOR>
 */

import _ from 'lodash';
import {router} from 'san-router';
import {NodeType} from 'san';
import {Tab, TabPanel} from '@baiducloud/bce-ui/san';

import {asComponent} from '../../decorators';
import {isString, redirect} from '../../helpers/utils';
import './app-tab-page.less';

@asComponent('@app-tab-page')
class AppTabPage extends Tab {

    get template() {
        return `<div class="app-tab-page">${super.template}</div>`
    }

    attached() {
        super.attached && super.attached();
        this.activeCulPanel();
    }

    activeCulPanel() {
        // 排除带有查询参数的部分
        const exceptQueryParams = router.locator.current.split('?')[0];
        // 根据URL解析当前显示的panel
        const curHash = '#' + exceptQueryParams;

        const tabPanels = this.getTabPanels();
        for (let i = 0; i < tabPanels.length; i++) {
            const panel = tabPanels[i];
            let url = panel.data
                ? panel.data.get('url')
                : panel.nodeType === NodeType.IF && panel.children.length
                    ? panel.children[0].data.get('url')
                    : null;
            url = url && url.split('?')[0];
            if (url && url === curHash) {
                this.data.set('selectedIndex', i);
                break;
            }
        }
    }
}

@asComponent('@app-tab-page-panel')
class TabPagePanel extends TabPanel {

    get template() {
        return `<div class="app-tab-page-panel">${super.template}</div>`
    }

    initData() {
        return _.extend(super.initData(), {
            url: null
        });
    }

    inited() {
        super.inited && super.inited();
        this.watch('active', value => {
            if (value) {
                let url = this.data.get('url');
                // 排除带有查询参数的部分
                const exceptQueryParams = router.locator.current.split('?')[0];
                const curHash = '#' + exceptQueryParams;

                if (url === curHash) {
                    url = '#' + router.locator.current;
                }
                url && isString(url) && redirect(url, {silent: true});
            }
        });
    }
}

