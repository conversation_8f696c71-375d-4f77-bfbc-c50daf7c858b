/**
 * app detail cell
 *
 * @file app-detail-cell.js
 * <AUTHOR>
 */

import {Component} from 'san';

import './app-detail-cell.less';
import {html} from '../../../helpers/utils';

import {asComponent} from '../../../decorators';

@asComponent('@app-detail-cell')
class AppDetailCell extends Component {
    static template = html`
        <template class="app-detail-cell">
            <div class="detail-cell" s-for="item in datasource" style="{{computedStyle}}">
                <label class="label">{{item.label}}</label>
                <span class="value">
                    <slot var-item="{{item}}" name="c-{{item.slot}}">{{item.value | raw}}</slot>
                </span>
            </div>
        </template>
    `;

    static computed = {
        computedStyle() {
            const divide = this.data.get('divide');
            const width = Math.floor(100 / divide) + '%';

            return {width};
        }
    };

    initData() {
        return {
            datasource: [],
            divide: 3
        };
    }
}
