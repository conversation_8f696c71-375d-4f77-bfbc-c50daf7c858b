/**
 * app link
 *
 * @file app-link.js 兼容san-router的link标签
 * <AUTHOR>
 */

import {Component} from 'san';
import {Link, router} from 'san-router';

import {html} from '../../../helpers/utils';
import {asComponent} from '../../../decorators';

@asComponent('@app-link')
export default class AppLink extends Component {
    routeListener: any;

    static template = html`
        <a href="{{href}}"
            onclick="return false;"
            on-click="clicker($event)"
            target="{{target}}"
            style="{{style}}"
            class="{{class}} {{isActive ? activeClass : ''}}">
            <slot/>
        </a>
    `;

    static computed = {
        href() {
            const link = this.data.get('to');
            if (!link) {
                return 'javascript: void(0)';
            }

            const href = Link.computed.href.call(this);

            //  #/xxxx/#/xxx 场景
            if (href && href.indexOf('#') > 0) {
                return href.replace(/^#/, '');
            }

            return router.mode !== 'hash'
                ? href.replace(/^#/, '')
                : `#${href}`;
        }
    };

    inited() {
        this.routeListener = (opt: any) => {
            this.data.set('isActive', opt.url === this.data.get('href'));
        };
        this.routeListener({url: router.locator.current});

        router.listen(this.routeListener);
    }

    disposed() {
        router.unlisten(this.routeListener);
    }

    initData() {
        return {
            isActive: false,
        };
    }

    clicker(evt: Event) {
        var href = this.data.get('href');

        if (typeof href === 'string' && /^#/.test(href)) {
            router.locator.redirect(href.replace(/^#/, ''));

            if (evt.preventDefault) {
                evt.preventDefault();
            }
            else {
                evt.returnValue = false;
            }
        }
    }
}
