/**
 * app pagination
 *
 * @file app-pagination.js
 * <AUTHOR>
 */

import {Component} from 'san';
import {Pager, Select, TextBox, Button} from '@baiducloud/bce-ui/san';

import './app-pagination.less';
import {html} from '../../../helpers/utils';

import {asComponent} from '../../../decorators';

@asComponent('@app-pagination')
class APPPagination extends Component {
    static template = html`
        <div class="app-pagination">
            <div class="page-size-sel">
                <label>每页展示</label>
                <ui-select
                    width="{{80}}"
                    datasource="{{value.datasource}}"
                    value="{=value.pageSize=}"
                    on-change="onPagerSizeChange"
                />
            </div>

            <ui-pager
                size="{{value.pageSize}}"
                page="{{value.pageNo}}"
                count="{{value.totalCount}}"
                back-text="<"
                forward-text=">"
                on-change="onPagerNoChange">
            </ui-pager>

            <div class="go-to">
                <label>跳转至</label>
                <ui-textbox
                    class="go-textbox"
                    value="{=goPagerNo=}"
                    width="{{40}}"
                    max="{{maxPageNo}}"
                    min="{{1}}"
                    on-enter="onGoPage"
                />
                <ui-button
                    disabled="{{goToDisabled}}"
                    class="go-button"
                    on-click="onGoPage"
                >
                    GO
                </ui-button>
            </div>
        </div>
    `;

    static components = {
        'ui-pager': Pager,
        'ui-select': Select,
        'ui-button': Button,
        'ui-textbox': TextBox
    }

    static computed = {
        maxPageNo() {
            const value = this.data.get('value');
            if (value && value.totalCount && value.pageSize) {
                return Math.ceil(value.totalCount / value.pageSize);
            }
            return 1;
        },
        goToDisabled() {
            const goPagerNo = this.data.get('goPagerNo');
            const maxPageNo = this.data.get('maxPageNo');
            // 必须是正整数
            if (goPagerNo
                && !isNaN(goPagerNo)
                && /^[0-9]*[1-9][0-9]*$/.test(goPagerNo)
                && goPagerNo >= 1
                && goPagerNo <= maxPageNo
            ) {
                return false;
            }
            return true;
        }
    }

    onGoPage() {
        const {goToDisabled, goPagerNo} = this.data.get();
        if (goToDisabled) {
            return;
        }
        this.data.set('goPagerNo', null);
        this.data.set('value.pageNo', parseInt(goPagerNo, 10));
        this.fireChange();
    }

    fireChange() {
        this.nextTick(() => {
            this.fire('pager-change', this.data.get('value'));
        });
    }

    onPagerSizeChange() {
        this.data.set('value.pageNo', 1);
        this.fireChange();
    }

    onPagerNoChange({pageNo}) {
        this.data.set('value.pageNo', pageNo);
        this.fireChange();
    }
}
