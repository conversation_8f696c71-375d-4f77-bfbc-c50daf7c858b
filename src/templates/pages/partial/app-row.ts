/**
 * app rpw
 *
 * @file app-legend.js
 * <AUTHOR>
 */

import {Component} from 'san';

import './app-row.less';
import {html} from '../../../helpers/utils';

import {asComponent} from '../../../decorators';

@asComponent('@app-row')
class AppRow extends Component {
    static template = html`
        <div class="app-row">
            <slot />
        </div>
    `;
}

@asComponent('@app-cell')
class AppCell extends Component {
    static template = html`
        <div class="app-cell">
            <label class="app-cell-label {{computedKlass}}">{{label}}</label>
            <slot />
        </div>
    `;

    static computed = {
        computedKlass() {
            return this.data.get('required') ? 'required' : '';
        }
    };
}