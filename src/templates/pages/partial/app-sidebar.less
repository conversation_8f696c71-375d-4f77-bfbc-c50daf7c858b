
.app-sidebar {
    width: 160px;
    height: 100%;
    display: flex;
    flex-shrink: 0;
    background: #fff;
    flex-flow: column;
    transition: all .3s;
    border-right: 1px solid #EBEBEB;

    &-title {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        height: 50px;
        font-size: 14px;
        color: #666666;
        padding-left: 20px;
        border-bottom: 1px solid #EBEBEB;

        .iconfont {
            margin-right: 10px;
        }
    }

    &-content {
        overflow-y: scroll;
        scrollbar-width: none; // Firefox
        -ms-overflow-style: none; // IE 10+

        &::-webkit-scrollbar { // WebKit
            width: 0;
            height: 0;
        }
    }

    &-item {
        display: flex;
        font-size: 12px;
        color: #000;
        position: relative;
        word-break: keep-all;
        flex-flow: column nowrap;

        .iconfont {
            top: 10px;
            right: 10px;
            font-size: 12px;
            color: #999;
            position: absolute;
            cursor: pointer;
            transform: rotate(90deg);
            transition: all .3s;
        }

        ul {
            max-height: 0;
            transition: all .3s;
            overflow: hidden;
        }

        a {
            flex: auto;
            display: flex;
            line-height: 40px;
            align-items: center;
            color: currentColor;
            padding-left: 20px;
            cursor: pointer;
        }

        &:before {
            content: '';
            width: 4px;
            height: 0px;
            top: 20px;
            position: absolute;
            transition:  all .3s;
        }

        & > a:hover {
            cursor: pointer;
            color: #108cee;
            background: #F5F5F5;
        }

        &&-active {
            color: #108cee;
            background: #F5F5F5;

            &::before {
                top: 0;
                height: 40px;
                background: #108cee;
            }
        }

        &&-expand {
            background: #F5F5F5;

            .iconfont {
                transform: rotate(-90deg) !important;

                & + ul {
                    max-height: 400px;
                }
            }
        }

        &&-disabled {
            color: #999;
            > a:hover {
                cursor: not-allowed;
                color: #999;
            }
        }

        &&-hidden {
            display: none;
        }
    }

    &-item &-item a:before {
        content: '';
        background: #ccc;
        height: 4px;
        width: 4px;
        border-radius: 4px;
        margin-right: 10px;
    }
}
