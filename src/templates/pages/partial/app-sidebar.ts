/**
 * AppSidebar
 *
 * @file AppSidebar.js
 * <AUTHOR>
 */

import _ from 'lodash';

import {Component, NodeType} from 'san';

import AppLink from './app-link';
import {asComponent} from '../../../decorators';
import {html, preventDefault, stopPropagation} from '../../../helpers/utils';

import './app-sidebar.less';

@asComponent('@app-sidebar-item')
class SidebarItem extends Component {
    static template = html`
        <li class="app-sidebar-item {{mainClass}}" on-click="clickHandler">
            <app-link to="{{linkTo}}">
                <slot name="title">
                    {{title}}
                </slot>
            </app-link>
            <template s-if="hasChildItemsSlots">
                <i class="iconfont icon-arrow-right"/>
                <ul>
                    <slot />
                </ul>
            </template>
        </li>
    `;

    static components = {
        'app-link': AppLink
    };

    initData() {
        return {
            trackId: 'sidebar_menu',
            trackName: '侧边栏导航',
            isActive: false,
            // 控制导航栏显隐
            visible: true,
            activeName: '',
            linkTo: null,
            expand: false,
            disabled: false // 是否禁用
        };
    }

    static computed = {
        mainClass() {
            const classname = [];
            const name = this.data.get('name');
            const activeName = this.data.get('activeName');
            const expand = this.data.get('expand');
            const disabled = this.data.get('disabled');
            const visible = this.data.get('visible');

            disabled && classname.push('app-sidebar-item-disabled');
            activeName && name === activeName && classname.push('app-sidebar-item-active');
            expand && classname.push('app-sidebar-item-expand');
            !visible && classname.push('app-sidebar-item-hidden');

            return classname.join(' ');
        }
    };

    inited() {
        if (this.sourceSlots) {
            const {noname = []} = this.sourceSlots;
            const childItem = _.filter(noname, item => item.tagName);
            const hasChildItemsSlots = childItem.length > 0;

            this.data.set('hasChildItemsSlots', hasChildItemsSlots);
        }
        this.watch('activeName', name => {
            const {name: selfName, active} = this.data.get();
            if (name === selfName) {
                // 通知父级菜单展开
                this.dispatch('hit-item');
            } else if (this.slotChildren.length > 0) {
                this.slotChildren.forEach(slotC => {
                    slotC.children.forEach(comp => {
                        if (comp.nodeType === NodeType.CMPT) {
                            comp.data.set('activeName', name);
                        }
                    });
                })
            } else if (active) {
                // 取消当前的active
                this.data.set('active', false);
            }
        });
    }

    clickHandler(e) {
        const {disabled, hasChildItemsSlots} = this.data.get();
        if (disabled) {
            return;
        }
        if (hasChildItemsSlots) {
            this.onToggleSubMenu();
            preventDefault(e);
        } else {
            this.onActive();
        }
        // 事件绑定在了li上面，二级菜单需要停止冒泡，防止触发一级菜单的事件
        stopPropagation(e);
    }

    onActive() {
        const name = this.data.get('name');
        this.dispatch('active', {name});
        this.fire('click');
    }

    onToggleSubMenu() {
        const toExpand = !this.data.get('expand');
        this.data.set('expand', toExpand);
        this.fire(toExpand ? 'open' : 'close');
    }

    static messages = {
        'hit-item'() {
            this.dispatch('hit-item');
            this.data.set('expand', true);
        }
    }
}

@asComponent('@app-sidebar')
export default class Sidebar extends Component {
    static Item = SidebarItem;

    static template = html`
        <div class="app-sidebar">
            <div class="app-sidebar-title" s-if="title">
                <i s-if="{{withBack}}" class="iconfont icon-arrow-left" on-click="back" />
                <i s-if="{{iconName}}" class="iconfont icon-{{iconName}}" />
                {{title}}
            </div>
            <ul class="app-sidebar-content">
                <slot />
            </ul>
        <div>
    `;

    static messages = {
        active({value}) {
            this.data.set('activeName', value.name);
        }
    }

    initData() {
        return {
            activeName: '',
            iconName: ''
        };
    }

    attached() {
        this.spreadChildren();
        this.watch('activeName', this.spreadChildren);
    }

    spreadChildren() {
        const name = this.data.get('activeName');
        this.slotChildren[0].children.forEach(comp => {
            if (comp.nodeType === NodeType.CMPT) {
                comp.data.set('activeName', name);
            }
        });
    }

    back() {
        this.fire('back', {});
    }
}
