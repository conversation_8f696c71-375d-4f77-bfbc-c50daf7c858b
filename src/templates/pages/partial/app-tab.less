.app-tab {
    display: flex;
    flex: auto;
    background: #f5f5f5;

    &-items {
        width: 140px;
        border-right: 1px solid #EBEBEB;
    }

    &-item {
        display: flex;
        line-height: 40px;
        cursor: pointer;
        padding-left: 20px;
        transition: background .3s;

        &:hover,
        &.tab-active {
            color: #108cee;
            background: #fff;
        }

        &.tab-active {
            padding-left: 16px;
            margin-right: -1px;
            border-left: 4px solid currentColor;
        }
    }

    &-content {
        display: none;
        flex: auto;
        background: #fff;

        &.tab-active {
            display: flex;
        }

        &>* {
            display: flex;
            flex: auto;
            flex-flow: column;
        }
    }
}
