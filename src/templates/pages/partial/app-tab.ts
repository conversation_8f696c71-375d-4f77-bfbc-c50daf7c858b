/**
 * normal tab page
 *
 * @file AppTab.js
 * <AUTHOR>
 */

import {NodeType, Component} from 'san';

import './app-tab.less';
import {html} from '../../../helpers/utils';
import {asComponent} from '../../../decorators';

@asComponent('@app-tab-content')
class AppTabContent extends Component {
    static template = html`
        <div class="app-tab-content {{computedClass}}">
            <slot s-if="actived" />
        </div>
    `;

    static computed = {
        computedClass() {
            return this.data.get('actived') ? 'tab-active' : '';
        }
    };

    initData() {
        return {
            actived: false,
            label: ''
        };
    }
}
@asComponent('@app-tab')
class AppTab extends Component {
    static template = html`
        <div class="app-tab">
            <div class="app-tab-items">
                <div s-for="item in labelItems"
                    class="app-tab-item {{item | computedClass}}"
                    on-click="onActive(item)">
                    {{item[0]}}
                </div>
            </div>
            <slot />
        </div>
    `;

    static filters = {
        computedClass(item) {
            return item[1] ? 'tab-active' : '';
        }
    };

    attached() {
        if (this.slotChildren[0]) {
            const labelItems = this.slotChildren[0].children
                .filter(cmpt => cmpt.nodeType === NodeType.CMPT)
                .map(cmpt => [cmpt.data.get('label'), cmpt.data.get('actived')]);
            this.data.set('labelItems', labelItems);
        }
    }

    onActive([label, isActived]) {
        if (!isActived) {
            const labelItems = this.data.get('labelItems').map(
                ([name]) => name === label
                    ? [name, true]
                    : [name, false]
            );

            this.data.set('labelItems', labelItems);
            this.slotChildren[0].children.forEach(cmpt => {
                if (cmpt.nodeType === NodeType.CMPT) {
                    cmpt.data.set('actived', label === cmpt.data.get('label'))
                }
            })
        }
    }
}
