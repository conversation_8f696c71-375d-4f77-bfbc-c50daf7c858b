/**
 * Copyright (c) 2020 Baidu Inc. All Rights Reserved
 *
 * This source code is licensed under the MIT license.
 * See LICENSE file in the project root for license information.
 *
 * @file framework.d.ts
 * <AUTHOR> (<EMAIL>)
 */

declare interface Framework {
    events: any;
    EVENTS: any;
    region: any;
    i18n: {
        getCurrentLanguage(): string;
    };
}

declare interface FrameworkContext {
    session: any;
    constants: any;
    flags: any;
    region: any;
}

declare interface DecoratorMetaType {
    name: string;
    scope?: number;
    desc?: string;
}
interface Window {
    /** 在bootstrap时赋值的fe-framework模块，必须在加载runtime资源之前存在，否则，部分导出内容可能拿不到 */
    $framework: any;
    __webpack_public_path__: string;
    amis?: any;
}

declare module 'san-router';
